@extends('layouts.admin')

@section('title', 'Edit User - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">User Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Users</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.users.show', $user) }}">{{ $user->name }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Edit</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Edit User Form -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Edit User: {{ $user->name }}</div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('admin.users.show', $user) }}" class="btn btn-info btn-sm">
                            <i class="ti ti-eye me-1"></i>View User
                        </a>
                        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back to Users
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.users.update', $user) }}">
                        @csrf
                        @method('PUT')
                        <div class="row gy-4">
                            <!-- Basic Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Basic Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <div class="col-xl-12">
                                                <label for="name" class="form-label">Full Name <span
                                                        class="text-danger">*</span></label>
                                                <input type="text"
                                                    class="form-control @error('name') is-invalid @enderror" id="name"
                                                    name="name" value="{{ old('name', $user->name) }}"
                                                    placeholder="Enter full name" required>
                                                @error('name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            <div class="col-xl-12">
                                                <label for="email" class="form-label">Email Address <span
                                                        class="text-danger">*</span></label>
                                                <input type="email"
                                                    class="form-control @error('email') is-invalid @enderror" id="email"
                                                    name="email" value="{{ old('email', $user->email) }}"
                                                    placeholder="Enter email address" required>
                                                @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            <div class="col-xl-12">
                                                <label for="username" class="form-label">Username</label>
                                                <input type="text"
                                                    class="form-control @error('username') is-invalid @enderror"
                                                    id="username" name="username"
                                                    value="{{ old('username', $user->username) }}"
                                                    placeholder="Enter username (optional)">
                                                @error('username')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Username is optional. If not provided, email will be
                                                    used for login.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Role & Account Status -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Role & Account Status</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <div class="col-xl-12">
                                                <label for="role" class="form-label">User Role <span
                                                        class="text-danger">*</span></label>
                                                <select class="form-select @error('role') is-invalid @enderror"
                                                    id="role" name="role" required
                                                    {{ $user->id === auth()->id() && $user->isAdmin() ? 'disabled' : '' }}>
                                                    <option value="">Select Role</option>
                                                    <option value="admin"
                                                        {{ old('role', $user->role) === 'admin' ? 'selected' : '' }}>Admin
                                                    </option>
                                                    <option value="employee"
                                                        {{ old('role', $user->role) === 'employee' ? 'selected' : '' }}>
                                                        Employee</option>
                                                    <!--<option value="user"
                                                            {{ old('role', $user->role) === 'user' ? 'selected' : '' }}>
                                                            User</option> -->
                                                </select>
                                                @if ($user->id === auth()->id() && $user->isAdmin())
                                                    <input type="hidden" name="role" value="{{ $user->role }}">
                                                    <div class="form-text text-warning">You cannot change your own admin
                                                        role.</div>
                                                @endif
                                                @error('role')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            <div class="col-xl-12">
                                                <label for="account_status" class="form-label">Account Status <span
                                                        class="text-danger">*</span></label>
                                                <select class="form-select @error('account_status') is-invalid @enderror"
                                                    id="account_status" name="account_status" required
                                                    {{ $user->id === auth()->id() ? 'disabled' : '' }}>
                                                    <option value="active"
                                                        {{ old('account_status', $user->isLocked() ? 'locked' : 'active') === 'active' ? 'selected' : '' }}>
                                                        Active</option>
                                                    <option value="locked"
                                                        {{ old('account_status', $user->isLocked() ? 'locked' : 'active') === 'locked' ? 'selected' : '' }}>
                                                        Locked</option>
                                                </select>
                                                @if ($user->id === auth()->id())
                                                    <input type="hidden" name="account_status" value="active">
                                                    <div class="form-text text-warning">You cannot lock your own account.
                                                    </div>
                                                @endif
                                                @error('account_status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            @if ($user->failed_login_attempts > 0)
                                                <div class="col-xl-12">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox"
                                                            id="reset_failed_attempts" name="reset_failed_attempts"
                                                            value="1">
                                                        <label class="form-check-label" for="reset_failed_attempts">
                                                            Reset failed login attempts
                                                            ({{ $user->failed_login_attempts }})
                                                        </label>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Password Change (Optional) -->
                            <div class="col-xl-12">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Change Password (Optional)</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-warning">
                                            <i class="ri-information-line me-2"></i>
                                            Leave password fields empty if you don't want to change the user's password.
                                        </div>
                                        <div class="row gy-3">
                                            <div class="col-xl-6">
                                                <label for="password" class="form-label">New Password</label>
                                                <div class="input-group">
                                                    <input type="password"
                                                        class="form-control @error('password') is-invalid @enderror"
                                                        id="password" name="password" placeholder="Enter new password">
                                                    <button class="btn btn-light" type="button"
                                                        onclick="togglePassword('password', this)">
                                                        <i class="ri-eye-off-line align-middle"></i>
                                                    </button>
                                                    @error('password')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="form-text">Password must be at least 8 characters long.</div>
                                            </div>
                                            <div class="col-xl-6">
                                                <label for="password_confirmation" class="form-label">Confirm New
                                                    Password</label>
                                                <div class="input-group">
                                                    <input type="password"
                                                        class="form-control @error('password_confirmation') is-invalid @enderror"
                                                        id="password_confirmation" name="password_confirmation"
                                                        placeholder="Confirm new password">
                                                    <button class="btn btn-light" type="button"
                                                        onclick="togglePassword('password_confirmation', this)">
                                                        <i class="ri-eye-off-line align-middle"></i>
                                                    </button>
                                                    @error('password_confirmation')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Information -->
                            <div class="col-xl-12">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Account Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Account Created</label>
                                                    <div class="form-control-plaintext">
                                                        {{ $user->created_at->format('M d, Y \a\t g:i A') }}</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Last Updated</label>
                                                    <div class="form-control-plaintext">
                                                        {{ $user->updated_at->format('M d, Y \a\t g:i A') }}</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Email Verified</label>
                                                    <div class="form-control-plaintext">
                                                        @if ($user->email_verified_at)
                                                            <span class="badge bg-success">Verified on
                                                                {{ $user->email_verified_at->format('M d, Y') }}</span>
                                                        @else
                                                            <span class="badge bg-warning">Not Verified</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                            @if ($user->isLocked())
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">Account Locked Until</label>
                                                        <div class="form-control-plaintext">
                                                            <span
                                                                class="badge bg-danger">{{ $user->locked_until->format('M d, Y \a\t g:i A') }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Role Information -->
                            <div class="col-xl-12">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Role Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <h6 class="fw-semibold">Role Descriptions:</h6>
                                            <ul class="mb-0">
                                                <li><strong>Admin:</strong> Full access to all system features including
                                                    user management, field management, and system settings.</li>
                                                <li><strong>Employee:</strong> Can create and manage their own bookings,
                                                    view calendar, and access standard features.</li>
                                                <li><strong>User:</strong> Limited access focused on viewing and basic
                                                    booking functionality.</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="col-xl-12">
                                <div class="d-flex gap-2 justify-content-end">
                                    <a href="{{ route('admin.users.show', $user) }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-device-floppy me-1"></i>Update User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function togglePassword(fieldId, button) {
            const field = document.getElementById(fieldId);
            const icon = button.querySelector('i');

            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'ri-eye-line align-middle';
            } else {
                field.type = 'password';
                icon.className = 'ri-eye-off-line align-middle';
            }
        }
    </script>
@endsection
